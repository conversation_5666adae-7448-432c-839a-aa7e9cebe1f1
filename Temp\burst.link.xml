<linker>
 <assembly fullname="Unity.Burst">
  <type fullname="Unity.Burst.LowLevel.BurstCompilerService">
   <method name="GetOrCreateSharedMemory"/>
  </type>
 </assembly>
 <assembly fullname="Unity.Collections">
  <type fullname="Unity.Collections.AllocatorManager">
   <method name=".cctor"/>
  </type>
  <type fullname="Unity.Collections.AllocatorManager.SharedStatics.TableEntry">
   <method name=".cctor"/>
  </type>
  <type fullname="Unity.Jobs.LowLevel.Unsafe.JobsUtility">
   <method name="get_ThreadIndexCount"/>
  </type>
  <type fullname="Unity.Collections.LowLevel.Unsafe.UnsafeUtility">
   <method name="MallocTracked"/>
  </type>
  <type fullname="Unity.Collections.LowLevel.Unsafe.UnsafeUtility">
   <method name="FreeTracked"/>
  </type>
 </assembly>
</linker>
