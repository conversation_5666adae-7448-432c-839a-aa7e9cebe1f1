{"player_build_data": [{"type": "il2cppData", "msg": {"build_event_id": "cfdd3b13065fd054c953d659c43b10cc", "node_executed": true, "attribute_total_count_eager_static_constructor": 20, "attribute_total_count_set_option": 0, "attribute_total_count_generate_into_own_cpp_file": 0, "attribute_total_count_ignore_by_deep_profiler": 44, "extra_types_total_count": 0, "option_extra_types_file_count": 0, "option_debug_assembly_name_count": 0, "option_additional_cpp_count": 0, "option_emit_null_checks": true, "option_enable_stacktrace": false, "option_enable_deep_profiler": false, "option_enable_stats": false, "option_enable_array_bounds_check": true, "option_enable_divide_by_zero_check": false, "option_emit_comments": false, "option_disable_generic_sharing": false, "option_maximum_recursive_generic_depth": -1, "option_generic_virtual_method_iterations": -1, "option_code_generation_option": ["EnableInlining"], "option_file_generation_option": [], "option_generics_option": [], "option_feature": [], "option_diagnostic_option": [], "option_convert_to_cpp": true, "option_compile_cpp": false, "option_development_mode": false, "option_enable_debugger": false, "option_generate_usym_file": false, "option_jobs": 24}, "version": 1}, {"type": "playerBuildTimingData", "msg": {"build_event_id": "cfdd3b13065fd054c953d659c43b10cc", "build_player": 128880, "preprocess_player": 310, "produce_player_script_assemblies": 7872, "build_scripts_dlls": 51, "postprocess_built_player": 118594, "node_summary_table": [{"name": "Csc", "duration": 1531}, {"name": "il2cpp.exe", "duration": 15645}, {"name": "UnityLinker.exe", "duration": 9423}]}, "version": 1}, {"type": "unityLinkerData", "msg": {"build_event_id": "cfdd3b13065fd054c953d659c43b10cc", "node_executed": true, "attribute_marked_count_always_link_assembly": 1, "attribute_swept_count_always_link_assembly": 0, "attribute_total_count_always_link_assembly": 1, "attribute_marked_count_preserve": 116, "attribute_total_count_preserve": 80, "attribute_total_count_preserve_body": 0, "attribute_marked_count_required_member": 233, "attribute_swept_count_required_member": 44, "attribute_total_count_required_member": 277, "attribute_marked_count_require_derived": 0, "attribute_swept_count_require_derived": 0, "attribute_total_count_require_derived": 0, "attribute_marked_count_require_implementors": 0, "attribute_swept_count_require_implementors": 0, "attribute_total_count_require_implementors": 0, "attribute_total_count_required_interface": 0, "attribute_marked_count_require_attribute_usages": 635, "attribute_total_count_require_attribute_usages": 2, "attribute_total_count_dynamic_dependency": 0, "attribute_marked_count_dynamic_dependency": 11, "attribute_swept_count_dynamic_dependency": -11, "assembly_counts_total_in": 108, "assembly_counts_link": 53, "assembly_counts_copy": 9, "assembly_counts_delete": 46, "assembly_counts_total_out": 62, "unresolved_stubbing_total_count": 0, "unresolved_stubbing_missing_interface_method_count": 0, "unresolved_stubbing_missing_abstract_class_method_count": 0, "unresolved_stubbing_missing_type_count": 0, "unresolved_stubbing_missing_method_count": 0, "unrecognized_reflection_access_total_count": 369, "unrecognized_reflection_access_core_count": 174, "unrecognized_reflection_access_unity_count": 185, "unrecognized_reflection_access_user_count": 10, "recognized_reflection_access_total_count": 53, "recognized_reflection_access_core_count": 29, "recognized_reflection_access_unity_count": 18, "recognized_reflection_access_user_count": 6, "link_xml_total_count": 11, "link_xml_embedded_count": 1, "link_xml_embedded_unity_count": 0, "link_xml_embedded_user_count": 0, "link_xml_file_count": 10, "link_xml_assembly_preserve_all_total_count": 0, "link_xml_assembly_preserve_all_unity_count": 0, "link_xml_assembly_preserve_all_core_count": 0, "link_xml_assembly_preserve_all_user_count": 0, "engine_module_total_in": 0, "engine_module_deleted": 0, "engine_module_total_out": 0, "option_rule_set": "Conservative", "option_enable_report": false, "option_enable_snapshot": false, "option_enable_engine_module_stripping": false, "option_unity_root_strategy": "UltraConservative", "option_enable_ildump": false}, "version": 1}]}